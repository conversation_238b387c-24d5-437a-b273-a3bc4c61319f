<template>
  <div class="bg-white p-2 rounded text-sm">
    <div class="font-semibold text-primary mb-1">Thông tin đơn hàng</div>
    <div class="space-x-1">
      <span class="font-semibold">Mã đơn hàng:</span>
      <span>{{ order?.id }}</span>
    </div>
    <div class="space-x-1">
      <span class="font-semibold">Tên:</span>
      <span>{{ order?.order?.ownerName }}</span>
    </div>
    <div class="space-x-1">
      <span class="font-semibold">SĐT:</span>
      <span>{{ order?.order?.ownerPhone }}</span>
    </div>
    <div class="space-x-1 flex items-center">
      <span class="font-semibold">Trạng thái đơn:</span>
      <span :class="getOrderStatusClass(order?.status)">{{
        order?.statusDescription
      }}</span>
      <span v-if="order?.order?.note" v-tippy="order?.order?.note">
        <svg
          xmlns="http://www.w3.org/2000/svg"
          fill="none"
          viewBox="0 0 24 24"
          stroke-width="1.5"
          stroke="currentColor"
          class="size-4"
        >
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            d="m11.25 11.25.041-.02a.75.75 0 0 1 1.063.852l-.708 2.836a.75.75 0 0 0 1.063.853l.041-.021M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Zm-9-3.75h.008v.008H12V8.25Z"
          />
        </svg>
      </span>
    </div>
    <div class="space-x-1 flex">
      <span class="font-semibold">Trạng thái FFM:</span>
      <span :class="getFFMStatusClass(order?.order?.fulfillmentStatus)">{{
        getFFMStatusText(order?.order?.fulfillmentStatus)
      }}</span>
      <span v-if="order?.order?.note" v-tippy="order?.order?.note">
        <svg
          xmlns="http://www.w3.org/2000/svg"
          fill="none"
          viewBox="0 0 24 24"
          stroke-width="1.5"
          stroke="currentColor"
          class="size-4"
        >
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            d="m11.25 11.25.041-.02a.75.75 0 0 1 1.063.852l-.708 2.836a.75.75 0 0 0 1.063.853l.041-.021M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Zm-9-3.75h.008v.008H12V8.25Z"
          />
        </svg>
      </span>
    </div>
    <div class="space-x-1 pb-2">
      <span class="font-semibold">Kênh bán hàng:</span>
      <span>{{ order?.order?.shopName }}</span>
    </div>
    <div class="border-t py-2">
      <div class="font-semibold text-primary mb-1">Thông tin kho</div>
      <div class="flex items-center gap-2">
        <label for="warehouse-select" class="block font-semibold">Kho: </label>
        <select
          id="warehouse-select"
          class="block w-full outline-none bg-secondary px-2 py-1 rounded cursor-pointer border"
          v-model="selectedWarehouse"
          @change="handleWarehouseChange"
        >
          <option
            v-for="warehouse in dataWarehouse"
            :key="warehouse.id"
            :value="warehouse.id"
          >
            {{ warehouse.name }}
          </option>
        </select>
      </div>
      <div class="flex items-center gap-1">
        <span class="font-semibold">SĐT:</span>
        <span>{{ dataInfoWareHouse?.phone }}</span>
      </div>
      <div>
        <span class="font-semibold">Địa chỉ kho hàng:</span>

        {{
          `${dataInfoWareHouse?.address?.address1 || ""},${
            dataInfoWareHouse?.address?.district || ""
          },${dataInfoWareHouse?.address?.province || ""}  `
        }}
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
const props = defineProps(["order"]);
const { getInforWarehouse } = useWarehouse();
const listWarehouse = useCookie("warehouse");
const selectedWarehouse = ref();
const dataWarehouse = ref<any>([]);
const handleInfoWarehouse = (warehouse: any) => {
  warehouse?.forEach(async (item: string) => {
    const response = await getInforWarehouse(item);
    dataWarehouse.value.push(response);
    handleGetWareHouse();
  });
};
onMounted(async () => {
  selectedWarehouse.value = props.order?.order?.customAttribute?.facilityId;
  await handleInfoWarehouse(listWarehouse.value);
});
const orderStore = useOrderStore();
const dataInfoWareHouse = ref();
const handleWarehouseChange = async () => {
  handleGetWareHouse();
  await orderStore.handleUpdateWarehouse(
    props.order?.id,
    selectedWarehouse.value
  );
  await orderStore.getOrderById(props.order?.id);
};
const handleGetWareHouse = () => {
  dataInfoWareHouse.value = dataWarehouse.value.find(
    (warehouse: any) => warehouse.id === selectedWarehouse.value
  );
};
// Import utilities
import {
  getOrderStatusClass,
  getFFMStatusClass,
  getFFMStatusText,
} from "~/utils/statusHelpers";
</script>
