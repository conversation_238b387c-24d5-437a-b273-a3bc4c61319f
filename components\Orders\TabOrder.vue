<template>
  <div class="bg-white my-2 p-2 relative">
    <!-- ✅ Removed Transition wrapper for faster loading -->
    <div v-if="isLoading" class="flex gap-2 p-1">
      <div
        v-for="item in 5"
        :key="`skeleton-${item}`"
        class="bg-secondary h-6 w-[130px] rounded"
      ></div>
      <div class="w-6 h-6 bg-secondary"></div>
      <div class="w-6 h-6 bg-secondary"></div>
    </div>

    <div
      v-else
      class="flex gap-2 items-center justify-between overflow-x-auto relative"
    >
      <div class="flex items-center gap-2 relative" ref="tabContainer">
        <div class="flex gap-2">
          <button
            v-for="(order, index) in memoizedListOrder"
            :key="`order-${order.id}`"
            :ref="(el) => setTabRef(el, index)"
            @click="handleClickOrder(order.id, index)"
            :class="getOrderButtonClass(order.id)"
            :disabled="isLoad"
            class="px-2 py-1 rounded"
          >
            <!-- Simplified button content -->
            <span class="relative z-10">
              {{ getOrderDisplayText(order.id) }}
            </span>
          </button>
        </div>

        <!-- Action buttons with hover animations -->
        <div class="flex gap-2">
          <button
            @click="handleCreateOrderTemp"
            :class="createButtonClass"
            :disabled="isLoad"
            class="relative"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
              stroke-width="1.5"
              stroke="currentColor"
              class="size-6"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                d="M12 4.5v15m7.5-7.5h-15"
              />
            </svg>
          </button>

          <button
            @click="handleReloadListOrder"
            :class="reloadButtonClass"
            :disabled="isLoad"
            class="relative"
            :style="{
              transform: isReloading ? 'rotate(360deg)' : 'rotate(0deg)',
            }"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
              stroke-width="1.5"
              stroke="currentColor"
              class="size-6"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                d="M16.023 9.348h4.992v-.001M2.985 19.644v-4.992m0 0h4.992m-4.993 0 3.181 3.183a8.25 8.25 0 0 0 13.803-3.7M4.031 9.865a8.25 8.25 0 0 1 13.803-3.7l3.181 3.182m0-4.991v4.99"
              />
            </svg>
          </button>
        </div>
      </div>

      <!-- ✅ Simplified ActionButtonOrder loading -->
      <Suspense>
        <ActionButtonOrder :diary="orderStore.orderDetail" />
        <template #fallback>
          <!-- ✅ Simple skeleton without animation -->
          <div class="flex gap-2 items-center justify-end">
            <div class="w-8 h-8 bg-gray-200 rounded"></div>
            <div class="w-8 h-8 bg-gray-200 rounded"></div>
            <div class="w-8 h-8 bg-gray-200 rounded"></div>
          </div>
        </template>
      </Suspense>
    </div>
  </div>

  <!-- ✅ Removed Teleport and Transition for faster loading -->
</template>

<script setup lang="ts">
// Props and composables
const props = defineProps<{
  isLoad?: boolean;
}>();

// Emits
const emit = defineEmits<{
  "update:loading": [value: boolean];
  "tab-loading": [value: boolean];
}>();

const orderStore = useOrderStore();
const route = useRoute();
const router = useRouter();
const tagStore = useTagStore();

// ✅ Use tab context (moved from onMounted logic)
const { storeId, orgId } = useTabContext();

// Reactive state
const isLoading = computed(() => props.isLoad || false); // Sync with page loading
const isReloading = ref(false);
const isInternalLoading = ref(false); // For internal operations like reload
const selectedOrderTerm = ref<string | number>(1);

// Animation state
const underlineLeft = ref(0);
const underlineWidth = ref(0);
const tabContainer = ref<HTMLElement>();
const tabRefs = ref<(HTMLElement | null)[]>([]);

// Removed ripple effect state for performance optimization

// Memoized computed properties for better performance
const memoizedListOrder = computed(() => {
  return orderStore.listOrder || [];
});

// Optimized computed classes to avoid inline calculations
const createButtonClass = computed(
  () =>
    `bg-primary text-white px-2 py-1 rounded ${
      props.isLoad ? "opacity-50 cursor-not-allowed" : ""
    }`
);

const reloadButtonClass = computed(
  () =>
    `bg-primary text-white px-2 py-1 rounded ${
      props.isLoad ? "opacity-50 cursor-not-allowed" : ""
    }`
);

// Simplified and reactive computed properties
const getOrderButtonClass = (orderId: string | number) => {
  return selectedOrderTerm.value === orderId
    ? "bg-primary text-white"
    : "border border-primary text-primary";
};

const getOrderDisplayText = (orderId: string | number) => {
  return isTimestamp(String(orderId)) ? "Đơn mới" : String(orderId);
};

const isTimestamp = (id: string): boolean => {
  return /^\d{13}$/.test(id);
};

// Optimized options object (removed duplicate)
const options = reactive({
  date_create_from: Date.now().toString(),
  currentPage: 1,
  maxResult: 5,
  status: [10],
});

// Animation helper methods
const setTabRef = (el: any, index: number) => {
  if (el && el.$el) {
    tabRefs.value[index] = el.$el;
  } else if (el) {
    tabRefs.value[index] = el;
  }
};

// Optimized underline update with immediate response
const updateUnderlineImmediate = (index: number) => {
  // Use requestAnimationFrame for smooth, non-blocking animation
  requestAnimationFrame(() => {
    const targetTab = tabRefs.value[index];
    if (targetTab && tabContainer.value) {
      const containerRect = tabContainer.value.getBoundingClientRect();
      const tabRect = targetTab.getBoundingClientRect();

      underlineLeft.value = tabRect.left - containerRect.left;
      underlineWidth.value = tabRect.width;
    }
  });
};

// Async version for when nextTick is needed
const updateUnderline = async (index: number) => {
  await nextTick();
  updateUnderlineImmediate(index);
};

// Removed createRippleEffect as it's not being used for performance optimization

// ✅ Optimized click handler for instant response
const handleClickOrder = async (orderId: string, index?: number) => {
  if (props.isLoad) return;

  try {
    // Emit loading start when switching tabs
    emit("tab-loading", true);
    emit("update:loading", true);

    // Immediate UI feedback - no debounce for better responsiveness
    selectedOrderTerm.value = orderId;

    // Non-blocking underline update
    if (typeof index === "number") {
      updateUnderlineImmediate(index);
    }

    // Non-blocking navigation using tab context
    await router.replace(
      `/sale?orderId=${orderId}&orgId=${orgId.value}&storeId=${storeId.value}`
    );

    // Don't emit loading end here - let the page handle it when data is loaded
    // The page will turn off loading when order data is fully loaded
  } catch (error) {
    console.error("Error switching tab:", error);
    // Only emit loading end on error
    emit("tab-loading", false);
    emit("update:loading", false);
  }
};

// ✅ Simplified create order temp function - main logic moved to page sale
const handleCreateOrderTemp = () => {
  try {
    // Emit loading start when creating new order
    emit("tab-loading", true);
    emit("update:loading", true);

    const order = Date.now().toString();
    orderStore.handleAddOrderToListOrder(order);
    orderStore.orderDetail = null;
    tagStore.dataConnector = null;
    orderStore.customerInOrder = null;
    handleClickOrder(order);
  } catch (error) {
    console.error("Error creating temp order:", error);
    // Emit loading end on error
    emit("tab-loading", false);
    emit("update:loading", false);
  }
};

// ✅ Simplified reload function - updateOrderId logic moved to page sale
const handleReloadListOrder = async () => {
  if (isLoading.value || isReloading.value || isInternalLoading.value) return; // Prevent multiple simultaneous calls

  try {
    // Emit loading start when reloading
    emit("tab-loading", true);
    emit("update:loading", true);

    isReloading.value = true;
    isInternalLoading.value = true;

    await orderStore.getListOrder(options);

    // Update underline position after reload
    await nextTick();
    const currentIndex = memoizedListOrder.value.findIndex(
      (order: any) => order.id === selectedOrderTerm.value
    );
    if (currentIndex >= 0) {
      await updateUnderline(currentIndex);
    }
  } catch (error) {
    console.error("Error reloading order list:", error);
  } finally {
    isInternalLoading.value = false;

    // Emit loading end after reload
    emit("tab-loading", false);
    emit("update:loading", false);

    // Delay to show rotation animation
    setTimeout(() => {
      isReloading.value = false;
    }, 500);
  }
};

// ✅ Enhanced watcher with immediate initialization
const stopWatcher = watchEffect(
  () => {
    if (route.query.orderId) {
      const orderId = Array.isArray(route.query.orderId)
        ? route.query.orderId[0]
        : route.query.orderId;
      selectedOrderTerm.value = orderId || 1;
    } else if (memoizedListOrder.value?.length > 0) {
      // Fallback to first order if no orderId in route
      selectedOrderTerm.value = memoizedListOrder.value[0]?.id || 1;
    }
  },
  { flush: "sync" }
); // Run immediately

// ✅ Optimized watcher with immediate response and throttling
const throttledUpdateUnderline = useThrottleFn(() => {
  // Use requestAnimationFrame instead of nextTick for better performance
  requestAnimationFrame(() => {
    const currentIndex = memoizedListOrder.value.findIndex(
      (order: any) => order.id === selectedOrderTerm.value
    );
    if (currentIndex >= 0) {
      updateUnderlineImmediate(currentIndex);
    }
  });
}, 16); // ~60fps

// ✅ Enhanced watchers for immediate UI updates
watch(selectedOrderTerm, throttledUpdateUnderline, {
  flush: "post",
  immediate: true, // Update underline immediately when selectedOrderTerm changes
});

// Optimized list watcher with shallow comparison
watch(() => memoizedListOrder.value.length, throttledUpdateUnderline, {
  flush: "post",
  immediate: true, // Update underline when list changes
});

// ✅ Removed onMounted - component now syncs automatically via watchers
// selectedOrderTerm is synced via watchEffect with route.query.orderId
// underline position is updated via throttledUpdateUnderline watcher

// Cleanup on unmount
onUnmounted(() => {
  // stopWatcher();
});
</script>

<style scoped>
/* ✅ Minimal CSS for better performance */

/* ✅ Only essential styles remain */
</style>
