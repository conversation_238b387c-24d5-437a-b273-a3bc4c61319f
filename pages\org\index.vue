<template>
  <div
    class="h-[calc(100vh-56px)] bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 mt-[56px] overflow-y-auto"
  >
    <!-- Optimized Background Pattern -->
    <div class="absolute inset-0 overflow-hidden pointer-events-none">
      <div class="absolute -inset-10 opacity-15">
        <div
          class="absolute top-1/4 left-1/4 w-80 h-80 bg-primary/8 rounded-full blur-2xl animate-pulse-slow"
        ></div>
        <div
          class="absolute bottom-1/4 right-1/4 w-64 h-64 bg-blue-500/8 rounded-full blur-2xl animate-pulse-slow"
          style="animation-delay: 2s"
        ></div>
        <div
          class="absolute top-3/4 left-1/2 w-48 h-48 bg-purple-500/8 rounded-full blur-2xl animate-pulse-slow"
          style="animation-delay: 4s"
        ></div>
      </div>
    </div>

    <!-- Header Section -->
    <div class="relative z-10 pt-8 pb-12">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Page Title & Description -->
        <div class="text-center mb-12">
          <h2 class="text-4xl font-bold text-gray-900 mb-4">Chọn Tổ chức</h2>
          <p class="text-lg text-gray-600 max-w-2xl mx-auto">
            Chọn tổ chức bạn muốn truy cập để bắt đầu quản lý cửa hàng và bán
            hàng
          </p>
        </div>
      </div>
    </div>

    <!-- Organizations Grid -->
    <div class="relative z-10 pb-16">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Loading State -->
        <div
          v-if="isLoading"
          class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"
        >
          <div v-for="i in 6" :key="`skeleton-${i}`" class="animate-pulse">
            <div
              class="bg-white/60 backdrop-blur-sm rounded-2xl p-6 border border-white/20 shadow-lg"
            >
              <div class="flex items-center space-x-4">
                <div class="w-16 h-16 bg-gray-300 rounded-xl"></div>
                <div class="flex-1 space-y-2">
                  <div class="h-4 bg-gray-300 rounded w-3/4"></div>
                  <div class="h-3 bg-gray-300 rounded w-1/2"></div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Organizations List -->
        <div
          v-else-if="orgArray?.length > 0"
          class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"
        >
          <div
            v-for="(item, index) in orgArray"
            :key="`org-${item.name}-${index}`"
            class="group"
          >
            <div
              @click="handleSetOrg(item.name, item.role)"
              class="relative bg-white/85 backdrop-blur-md rounded-2xl p-6 border border-white/30 shadow-lg hover:shadow-xl transition-all duration-200 cursor-pointer will-change-transform hover:scale-1 hover:-translate-y-0.5"
              :class="{
                'ring-2 ring-primary ring-opacity-50':
                  selectedOrg === item.name,
              }"
            >
              <!-- Selection Indicator -->
              <div
                v-if="selectedOrg === item.name"
                class="absolute top-4 right-4"
              >
                <div
                  class="w-6 h-6 bg-primary rounded-full flex items-center justify-center"
                >
                  <svg
                    class="w-4 h-4 text-white"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                  >
                    <path
                      fill-rule="evenodd"
                      d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                      clip-rule="evenodd"
                    ></path>
                  </svg>
                </div>
              </div>

              <!-- Organization Content -->
              <div class="flex items-center space-x-4 mb-4">
                <div class="relative">
                  <div
                    class="w-16 h-16 bg-gradient-to-br from-primary to-blue-600 rounded-xl flex items-center justify-center shadow-lg group-hover:shadow-xl transition-shadow duration-200 will-change-transform"
                  >
                    <svg
                      class="w-8 h-8 text-white"
                      fill="currentColor"
                      viewBox="0 0 20 20"
                    >
                      <path d="M4 3a2 2 0 100 4h12a2 2 0 100-4H4z"></path>
                      <path
                        fill-rule="evenodd"
                        d="M3 8h14v7a2 2 0 01-2 2H5a2 2 0 01-2-2V8zm5 3a1 1 0 011-1h2a1 1 0 110 2H9a1 1 0 01-1-1z"
                        clip-rule="evenodd"
                      ></path>
                    </svg>
                  </div>
                  <!-- Online Indicator -->
                  <div
                    class="absolute -bottom-1 -right-1 w-5 h-5 bg-green-500 rounded-full border-2 border-white flex items-center justify-center"
                  >
                    <div
                      class="w-2 h-2 bg-white rounded-full animate-pulse"
                    ></div>
                  </div>
                </div>
                <div class="flex-1 min-w-0">
                  <h3
                    class="text-lg font-bold text-gray-900 truncate group-hover:text-primary transition-colors duration-150"
                  >
                    {{ item.name }}
                  </h3>
                  <p class="text-sm text-gray-500 mt-1">
                    {{ getRoleDisplayText(item.role) }}
                  </p>
                </div>
              </div>

              <!-- Role Badges -->
              <div class="flex gap-2 mb-4 overflow-hidden">
                <span
                  v-for="role in getDisplayRoles(item.role)"
                  :key="role"
                  class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium whitespace-nowrap flex-shrink-0"
                  :class="getRoleBadgeClass(role)"
                >
                  {{ getRoleLabel(role) }}
                </span>
                <span
                  v-if="getHiddenRolesCount(item.role) > 0"
                  class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-600 whitespace-nowrap flex-shrink-0"
                >
                  +{{ getHiddenRolesCount(item.role) }} khác
                </span>
              </div>
              <!-- Loading Overlay -->
              <div
                v-if="selectedOrg === item.name && isLoading"
                class="absolute inset-0 bg-white/85 backdrop-blur-sm rounded-2xl flex items-center justify-center"
              >
                <div class="flex items-center space-x-3">
                  <LoadingSpinner />
                  <span class="text-sm font-medium text-gray-700"
                    >Đang kết nối...</span
                  >
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Empty State -->
        <div v-else-if="!isLoading" class="text-center py-16">
          <div
            class="w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-6"
          >
            <svg
              class="w-12 h-12 text-gray-400"
              fill="currentColor"
              viewBox="0 0 20 20"
            >
              <path d="M4 3a2 2 0 100 4h12a2 2 0 100-4H4z"></path>
              <path
                fill-rule="evenodd"
                d="M3 8h14v7a2 2 0 01-2 2H5a2 2 0 01-2-2V8zm5 3a1 1 0 011-1h2a1 1 0 110 2H9a1 1 0 01-1-1z"
                clip-rule="evenodd"
              ></path>
            </svg>
          </div>
          <h3 class="text-xl font-medium text-gray-900 mb-2">
            Không có tổ chức nào
          </h3>
          <p class="text-gray-500 mb-6">
            Bạn chưa được cấp quyền truy cập vào tổ chức nào.
          </p>
          <button
            @click="handleRefresh"
            class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-lg text-white bg-primary hover:bg-primary/90 transition-colors duration-150 will-change-transform"
          >
            <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
              <path
                fill-rule="evenodd"
                d="M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z"
                clip-rule="evenodd"
              ></path>
            </svg>
            Làm mới
          </button>
        </div>
      </div>
    </div>

    <!-- Loading Modal with Teleport -->
    <Teleport to="body">
      <LoadingSpinner
        v-if="isLoading && selectedOrg"
        text="Đang kết nối tổ chức"
        subText="Vui lòng đợi trong giây lát..."
      />
    </Teleport>
  </div>
</template>

<script setup lang="ts">
useHead({
  title: "Danh sách Org",
  meta: [
    {
      name: "description",
      content: "Danh sách org",
    },
  ],
});

definePageMeta({
  layout: "custom",
});

// Stores and composables
const authStore = useAuthStore();
const { hasPermission, setOrgId } = usePermission();
const router = useRouter();
const auth = useCookie("auth") as any;
const dataOrg = useCookie("dataOrg").value as any;
const useStore = useStoreStore();
const { getInfoChat } = useComhub();

// Reactive state
const orgArray = ref<any>();
const isLoading = ref(false);
const selectedOrg = ref<string | null>(null);

// Remove unused userInfo computed

// Role management functions
const getRoleDisplayText = (roles: string[]) => {
  if (!roles || roles.length === 0) return "Không có quyền";
  const primaryRole = roles[0];
  return getRoleLabel(primaryRole);
};

const getRoleLabel = (role: string) => {
  const roleLabels: Record<string, string> = {
    ORG_ADMIN: "Quản trị viên",
    SALE: "Bán hàng",
    SALE_ADMIN: "Quản lý bán hàng",
    REPORT: "Báo cáo",
    CUSTOMER_ADMIN: "Quản lý khách hàng",
    ADMIN_CUSTOMER: "Quản lý khách hàng",
    SALES: "Bán hàng",
    SALE_OP: "Vận hành bán hàng",
    SUPP_ADMIN: "Quản lý nhà cung cấp",
    SALE_MANAGER: "Trưởng phòng bán hàng",
    SUPP_OP: "Vận hành nhà cung cấp",
  };
  return roleLabels[role] || role;
};

const getRoleBadgeClass = (role: string) => {
  const roleClasses: Record<string, string> = {
    ORG_ADMIN: "bg-purple-100 text-purple-800",
    SALE: "bg-blue-100 text-blue-800",
    SALE_ADMIN: "bg-indigo-100 text-indigo-800",
    REPORT: "bg-green-100 text-green-800",
    CUSTOMER_ADMIN: "bg-yellow-100 text-yellow-800",
    ADMIN_CUSTOMER: "bg-yellow-100 text-yellow-800",
    SALES: "bg-blue-100 text-blue-800",
    SALE_OP: "bg-cyan-100 text-cyan-800",
    SUPP_ADMIN: "bg-orange-100 text-orange-800",
    SALE_MANAGER: "bg-red-100 text-red-800",
    SUPP_OP: "bg-pink-100 text-pink-800",
  };
  return roleClasses[role] || "bg-gray-100 text-gray-800";
};

// Smart role display functions
const getDisplayRoles = (roles: string[]) => {
  if (!roles || roles.length === 0) return [];

  const displayRoles = [];
  const maxLength = 22; // Maximum characters for role label

  // Check each role in order, stop when we find one that's too long
  for (let i = 0; i < roles.length && displayRoles.length < 2; i++) {
    const roleLabel = getRoleLabel(roles[i]);
    if (roleLabel.length <= maxLength) {
      displayRoles.push(roles[i]);
    } else {
      // If this role is too long, stop here and hide all remaining roles
      break;
    }
  }

  return displayRoles;
};

const getHiddenRolesCount = (roles: string[]) => {
  if (!roles || roles.length === 0) return 0;

  const displayRoles = getDisplayRoles(roles);
  return roles.length - displayRoles.length;
};

// Enhanced handleSetOrg with better UX
const handleSetOrg = async (name: any, role: any) => {
  if (isLoading.value) return; // Prevent multiple clicks

  selectedOrg.value = name;
  localStorage.removeItem("matrixToken");
  localStorage.removeItem("matrixUserId");

  isLoading.value = true;
  try {
    await setOrgId(name);
    nextTick();
    await Promise.allSettled([
      useStore.getData(auth.value.user.id),
      getInfoChat(),
    ]);

    if (useStore.store.length === 0) {
      useNuxtApp().$toast.error("Chưa có quyền vào store trong org này");
      return;
    }

    // Use tab-isolated context instead of cookies
    const { setContext, storeId } = useTabContext();
    await setContext(name, "N/A");

    if (hasPermission(role || [])) {
      const user = {
        id: auth.value?.user?.id,
        name: auth.value?.user?.name,
        email: auth.value?.user?.email,
        phone: auth.value?.user?.phone,
        avatar: auth.value?.user?.avatar,
        birthDate: auth.value?.user?.birthDate,
        roles: role,
      };
      authStore.setUser(user);

      // Navigate with smooth transition
      if (storeId.value === "N/A") {
        await router.push("/dashboard" + "?orgId=" + name);
      } else {
        await router.push("/diary" + "?storeId=" + storeId.value);
      }
    } else {
      useNuxtApp().$toast.error("Tài khoản không có quyền truy cập");
    }
  } catch (error) {
    console.error("Error setting org:", error);
    useNuxtApp().$toast.error("Có lỗi xảy ra khi kết nối tổ chức");
  } finally {
    isLoading.value = false;
    selectedOrg.value = null;
  }
};

// Handle refresh
const handleRefresh = () => {
  handleArray();
};

// Process organization data
const handleArray = () => {
  const data = Object.keys(dataOrg).map((key) => {
    return {
      name: key,
      role: dataOrg[key],
    };
  });
  orgArray.value = data.filter((item) => item.role.length > 0);

  // Auto-select if only one org
  if (orgArray.value.length === 1) {
    handleSetOrg(orgArray.value[0].name, orgArray.value[0].role);
  }
};

// Initialize on mount
onMounted(() => {
  handleArray();
});
</script>
<style scoped>
/* Optimized animations for 120Hz displays */
@keyframes gradient-shift {
  0%,
  100% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
}

/* Slower, more efficient pulse animation */
@keyframes pulse-slow {
  0%,
  100% {
    opacity: 0.8;
    transform: scale(1);
  }
  50% {
    opacity: 1;
    transform: scale(1.05);
  }
}

/* Optimized pulse for background elements */
.animate-pulse-slow {
  animation: pulse-slow 6s ease-in-out infinite;
  will-change: transform, opacity;
}

/* Apply optimized animations */
.bg-gradient-to-br {
  background-size: 200% 200%;
  animation: gradient-shift 20s ease infinite;
  will-change: background-position;
}

/* Smooth transitions for all interactive elements */
.group {
  opacity: 1;
  transform: translateY(0);
  will-change: transform;
}

/* Hardware acceleration for smooth scrolling */
html {
  scroll-behavior: smooth;
}

* {
  transform: translateZ(0);
  backface-visibility: hidden;
  perspective: 1000px;
}

/* Enhanced backdrop blur fallback */
@supports not (backdrop-filter: blur(12px)) {
  .backdrop-blur-md {
    background-color: rgba(255, 255, 255, 0.85);
  }
  .backdrop-blur-sm {
    background-color: rgba(255, 255, 255, 0.8);
  }
}

/* Custom scrollbar for mobile */
@media (max-width: 768px) {
  .overflow-x-auto::-webkit-scrollbar {
    display: none;
  }
  .overflow-x-auto {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }
}

/* Focus styles for accessibility */
.group > div:focus-visible {
  outline: 2px solid #3f51b5;
  outline-offset: 2px;
}

/* Loading overlay animation */
.absolute.inset-0 {
  backdrop-filter: blur(4px);
}
</style>
