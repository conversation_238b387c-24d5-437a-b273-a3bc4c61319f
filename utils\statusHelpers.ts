/**
 * Status Helper Utilities
 * Centralized functions for handling status classes and text display
 */

// Type definitions for better type safety
export type OrderStatus =
  | "REQUEST"
  | "OPEN"
  | "APPROVED"
  | "IN_PROGRESS"
  | "WAIT_DELIVERY"
  | "PROCESSING_DELIVERY"
  | "COMPLETED"
  | "RETURNING"
  | "RETURNED";

export type FFMStatus =
  | "OPEN"
  | "WAIT_DELIVERY"
  | "UNFULFILLED"
  | "PROCESSING_DELIVERY"
  | "FULFILLED"
  | "RETURNING"
  | "RETURNED";

export type PaymentStatus = "Đã thanh toán" | "Chờ thanh toán";

export type TimekeepingType = "CHECK_IN" | "CHECK_OUT";

// Status class mappings
const ORDER_STATUS_CLASSES: Record<OrderStatus, string> = {
  REQUEST: "text-[#5BC0EB] rounded-full px-2 py-1 text-sm",
  OPEN: "text-[#64B5F6] rounded-full px-2 py-1 text-sm",
  APPROVED: "text-[#4CAF50] rounded-full px-2 py-1 text-sm",
  IN_PROGRESS: "text-[#FFB74D] rounded-full px-2 py-1 text-sm",
  WAIT_DELIVERY: "text-[#FF9800] rounded-full px-2 py-1 text-sm",
  PROCESSING_DELIVERY: "text-[#2196F3] rounded-full px-2 py-1 text-sm",
  COMPLETED: "text-green-400 rounded-full px-2 py-1 text-sm",
  RETURNING: "text-[#BA68C8] rounded-full px-2 py-1 text-sm",
  RETURNED: "text-[#E57373] rounded-full px-2 py-1 text-sm",
} as const;

const FFM_STATUS_CLASSES: Record<FFMStatus, string> = {
  OPEN: "text-[#FFB74D] text-sm",
  WAIT_DELIVERY: "text-[#FF9800] text-sm",
  UNFULFILLED: "text-red-400 text-sm",
  PROCESSING_DELIVERY: "text-[#2196F3] text-sm",
  FULFILLED: "text-green-300 text-sm",
  RETURNING: "text-[#BA68C8] text-sm",
  RETURNED: "text-[#E57373] text-sm",
} as const;

const FFM_STATUS_TEXT: Record<FFMStatus, string> = {
  OPEN: "Đang xử lý",
  WAIT_DELIVERY: "Chờ ĐVVC lấy hàng",
  UNFULFILLED: "Hủy Fulfillment",
  PROCESSING_DELIVERY: "Đang giao",
  FULFILLED: "Hoàn thành",
  RETURNING: "Đang trả hàng",
  RETURNED: "Đã trả hàng",
} as const;

/**
 * Get CSS classes for order status
 * @param statusDescription - The order status
 * @param variant - Style variant (default, semibold, background)
 * @returns CSS class string
 */
export const getOrderStatusClass = (
  statusDescription: string,
  variant: "default" | "semibold" | "background" = "default"
): string => {
  if (variant === "background") {
    // Background variant with different colors
    const backgroundClasses: Record<OrderStatus, string> = {
      REQUEST: "bg-blue-500 text-white rounded-full px-2 py-1 text-sm",
      OPEN: "bg-[#34F0D1] text-black rounded-full px-2 py-1 text-sm",
      APPROVED: "bg-[#edf7ed] text-[#2e7d32] rounded-full px-2 py-1 text-sm",
      IN_PROGRESS: "bg-purple-500 text-white rounded-full px-2 py-1 text-sm",
      WAIT_DELIVERY: "bg-[#FEF0CD] text-black rounded-full px-2 py-1 text-sm",
      PROCESSING_DELIVERY:
        "bg-[#FEF0CD] text-black rounded-full px-2 py-1 text-sm",
      COMPLETED: "bg-[#edf7ed] text-[#2e7d32] rounded-full px-2 py-1 text-sm",
      RETURNING: "bg-yellow-500 text-black rounded-full px-2 py-1 text-sm",
      RETURNED: "bg-gray-500 text-white rounded-full px-2 py-1 text-sm",
    };
    return (
      backgroundClasses[statusDescription as OrderStatus] ||
      "bg-gray-500 text-white rounded-full px-2 py-1 text-sm"
    );
  }

  const baseClass = ORDER_STATUS_CLASSES[statusDescription as OrderStatus];
  if (!baseClass) {
    return "text-[#636AE8] rounded-full px-2 py-1 text-sm";
  }

  if (variant === "semibold") {
    return `${baseClass} font-semibold`;
  }

  return baseClass;
};

/**
 * Get CSS classes for FFM status
 * @param ffmStatus - The FFM status
 * @returns CSS class string
 */
export const getFFMStatusClass = (ffmStatus: string): string => {
  return FFM_STATUS_CLASSES[ffmStatus as FFMStatus] || "";
};

/**
 * Get display text for FFM status
 * @param ffmStatus - The FFM status
 * @returns Vietnamese display text
 */
export const getFFMStatusText = (ffmStatus: string): string => {
  return FFM_STATUS_TEXT[ffmStatus as FFMStatus] || "";
};

/**
 * Get CSS classes for payment status
 * @param paymentStatus - The payment status
 * @param variant - Style variant
 * @returns CSS class string
 */
export const getPaymentStatusClass = (
  paymentStatus: string,
  variant: "default" | "background" | "simple" = "default"
): string => {
  switch (variant) {
    case "background":
      switch (paymentStatus) {
        case "Đã thanh toán":
          return "bg-green-100 text-green-800 rounded-full px-2 py-1 text-sm";
        case "Chờ thanh toán":
          return "bg-yellow-100 text-green-800 rounded-full px-2 py-1 text-sm";
        default:
          return "bg-red-200 text-green-800 rounded-full px-2 py-1 text-sm";
      }
    case "simple":
      switch (paymentStatus) {
        case "Đã thanh toán":
          return "text-green-400 text-sm";
        case "Chờ thanh toán":
          return "text-yellow-400 text-sm";
        default:
          return "text-red-400 text-sm";
      }
    default:
      switch (paymentStatus) {
        case "Đã thanh toán":
          return "bg-green-100 text-green-800 flex items-center justify-center rounded-full py-1 text-sm mx-8";
        case "Chờ thanh toán":
          return "bg-yellow-100 text-green-800 flex items-center justify-center rounded-full py-1 text-sm mx-8";
        default:
          return "bg-red-200 text-green-800 rounded-full px-2 py-1 text-sm mx-6";
      }
  }
};

/**
 * Get CSS classes for timekeeping status
 * @param type - The timekeeping type
 * @returns CSS class string
 */
export const getTimekeepingStatusClass = (type: string): string => {
  const baseClass =
    "inline-flex items-center gap-2 px-4 py-2 rounded-full text-sm font-medium";

  switch (type) {
    case "CHECK_IN":
      return `${baseClass} bg-green-100 text-green-800`;
    case "CHECK_OUT":
      return `${baseClass} bg-red-100 text-red-800`;
    default:
      return `${baseClass} bg-gray-100 text-gray-800`;
  }
};

/**
 * Get display text for timekeeping type
 * @param type - The timekeeping type
 * @returns Vietnamese display text
 */
export const getTimekeepingTypeText = (type: string): string => {
  switch (type) {
    case "CHECK_IN":
      return "Vào ca";
    case "CHECK_OUT":
      return "Hết ca";
    default:
      return type;
  }
};

/**
 * Format status with HTML (legacy function - consider refactoring to return objects)
 * @param statusCode - Status code number
 * @param processResultCode - Process result code
 * @returns HTML string (deprecated approach)
 */
export const formatStatusHTML = (
  statusCode: number,
  processResultCode: any
): string => {
  if (statusCode === 4 || statusCode === 6) {
    switch (processResultCode) {
      case 1:
        return `<div class="min-w-[150px] text-center text-sm leading-5 font-medium px-3 py-[5px] rounded-md text-red-500">Thất bại</div>`;
      case 3:
        return `<div class="min-w-[150px] text-center text-sm leading-5 font-medium px-3 py-[5px] rounded-md text-green-500">Thành công</div>`;
      default:
        return "";
    }
  } else {
    switch (statusCode) {
      case 1:
        return `<div class="min-w-[150px] text-center text-sm leading-5 font-medium px-3 py-[5px] rounded-md" style="color: #34F0D1">Mới tạo</div>`;
      case 2:
        return `<div class="min-w-[150px] text-center text-sm leading-5 font-medium px-3 py-[5px] rounded-md text-red-500">Từ chối</div>`;
      case 3:
        return `<div class="min-w-[150px] text-center text-sm leading-5 font-medium px-3 py-[5px] rounded-md text-blue-500">Mới tạo</div>`;
      default:
        return "";
    }
  }
};
export const getItemOrderStatus = (status: number) => {
  switch (status) {
    case 20:
      return "Chính thức";
    case 30:
      return "Đang xử lý";
    case 35:
      return "Chờ DVVC lấy hàng";
    case 37:
      return "Đang giao";
    case 39:
      return "Đang trả hàng";
    case 80:
      return "Hoàn thành";
    case 90:
      return "Đã hủy";
    case 91:
      return "Đã trả hàng";
    case 11:
      return "Chưa xử lý";
    default:
      return status;
  }
};
