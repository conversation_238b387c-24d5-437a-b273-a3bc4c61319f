<template>
  <div class="space-y-2 bg-white p-2 rounded text-sm">
    <div class="flex items-center justify-between">
      <div class="font-semibold text-primary text-sm">Xuất kho</div>
      <div :class="handleGetClassStatusExport(dataFFM?.exportStatus)">
        {{ handleStatusExport(dataFFM?.exportStatus) }}
      </div>
    </div>
    <div class="w-full overflow-x-auto">
      <!-- tạo bảng có 5 cột -->
      <table class="table-auto w-full text-sm">
        <thead class="top-0 z-2">
          <tr class="text-left font-semibold bg-blue-50">
            <th class="p-2 w-6/12 text-center ">Tên sản phẩm</th>
            <th class="p-2 w-2/12">SL yêu cầu</th>
            <th class="p-2 w-2/12">SL tồn kho</th>
            <th class="p-2 w-2/12">SL xuất kho</th>
          </tr>
        </thead>
        <tbody>
          <tr
            v-for="orders in order?.activeOrderItemProfiles"
            :key="orders.id"
            class="hover:bg-blue-50 even:bg-gray-50 odd:bg-white"
          >
            <td class="max-w-0 overflow-hidden">
              <div>
                <ProductSimpleCard
                  :product="orders.orderLineItem"
                ></ProductSimpleCard>
              </div>
            </td>
            <td class="text-center">{{ orders?.orderLineItem?.quantity }}</td>
            <td class="text-center">
              {{ inventoryData[orders?.orderLineItem?.variant?.sku] ?? "0" }}
            </td>

            <td class="text-center">
              {{ orders?.orderLineItem?.quantity }}
            </td>
          </tr>
        </tbody>
      </table>
    </div>
    <div
      v-if="
        (dataFFM?.exportStatus === 'READY_TO_EXPORT' ||
          dataFFM?.exportStatus === 'PARTIAL_EXPORT') &&
        dataFFM?.fulfillmentStatus !== 'CANCELLED' &&
        order?.status !== 'CANCELLED'
      "
    >
      <button
        @click="handleConfirmExport"
        class="text-white bg-primary px-2 py-1 rounded w-full"
      >
        Xác nhận xuất kho
      </button>
    </div>
    <div>
      <ConfirmDialog
        v-if="isOpenConfirmPopup"
        title="Thông báo"
        :message="`Đơn hàng đang ở trạng thái 'Mới tạo' bạn có muốn cập nhật sang 'Đã xác nhận'`"
        @confirm="handleConfirm"
        @cancel="toogleConfirmDialog"
      ></ConfirmDialog>
      <LoadingSpinner v-if="isLoading" />
    </div>
  </div>
</template>
<script setup lang="ts">
const { confirmExport } = usePortal();
const props = defineProps(["order", "dataFFM"]);
const emit = defineEmits(["fetchFFMStatus"]);
const isLoading = ref(false);
const { getInventoryV2 } = useWarehouse();

const inventoryData = ref<Record<string, number>>({});
const fetchInventories = async () => {
  const items = props.order?.activeOrderItemProfiles || [];
  for (const item of items) {
    const product = item.orderLineItem;
    const key = product?.variant?.sku;

    const data = [
      {
        productId: product?.variant?.product?.id,
        variantId:
          product?.variant?.id === product?.variant?.product?.id
            ? ""
            : product?.variant?.id,
        sku: key,
      },
    ];
    try {
      const response = await getInventoryV2(
        props.order?.order?.customAttribute?.facilityId,
        data
      );
      inventoryData.value[key] = response[0]?.onHand ?? 0;
    } catch (error) {
      inventoryData.value[key] = 0;
    }
  }
};
const auth = useCookie("auth") as any;
const handleConfirmExport = async () => {
  if (props.order.status === "OPEN") {
    toogleConfirmDialog();
    return;
  }
  isLoading.value = true;
  try {
    const response = await confirmExport(props.order?.id, auth.value?.user?.id);
    if (response.status === 1) {
      await orderStore.updateOrder(props.order?.id);
      props.dataFFM.exportStatus = "EXPORTED";
      emit("fetchFFMStatus");
    }
  } catch (error) {
    throw error;
  } finally {
    isLoading.value = false;
  }
};
onMounted(async () => {
  await fetchInventories();
});
const handleStatusExport = (status: string) => {
  switch (status) {
    case "READY_TO_EXPORT":
      return "Chờ xuất kho";
      break;
    case "PARTIAL_EXPORT":
      return "Xuất kho 1 phần";
      break;
    case "CANCELLED":
      return "Hủy bỏ xuất kho";
    default:
      return "Hoàn thành xuất kho";
      break;
  }
};
const handleGetClassStatusExport = (status: string) => {
  switch (status) {
    case "READY_TO_EXPORT":
      return "text-yellow-400";
      break;
    case "PARTIAL_EXPORT":
      return "text-orange-400";
      break;
    case "CANCELLED":
      return "text-red-400";
    default:
      return "text-green-400";
      break;
  }
};
watch(
  () => props.order,
  async (newVal, oldVal) => {
    await fetchInventories();
  },
  { deep: true, immediate: true }
);
const isOpenConfirmPopup = ref(false);
const orderStore = useOrderStore();
const toogleConfirmDialog = async () => {
  isOpenConfirmPopup.value = !isOpenConfirmPopup.value;
};
const { updateStatusApprovedV2 } = useOrder();
const handleConfirm = async () => {
  isLoading.value = true;
  await updateStatusApprovedV2(props.order?.id);
  await orderStore.updateOrder(props.order?.id);
  isLoading.value = false;
};
import { getItemOrderStatus } from "~/utils/statusHelpers";
</script>
